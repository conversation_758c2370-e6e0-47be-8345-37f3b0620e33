"use client";
import Link from "next/link";
import { useState } from "react";

const Navbar = () => {
  const [hovered, setHovered] = useState<number | null>(null);
  const navItems = [
    { name: "About", href: "/about" },
    { name: "Projects", href: "/projects" },
    { name: "Contact", href: "/contact" },
    { name: "Blog", href: "/blogs" },
  ];
  return (
    <nav className="flex items-center justify-between ">
      <span>QStar</span>
      <div className="items0-center flex">
        {navItems.map((item, idx) => (
          <Link
            key={idx}
            href={item.href}
            onMouseEnter={() => setHovered(idx)}
            onMouseLeave={() => setHovered(null)}
            className="relative px-2 py-1 text-sm font-medium text-white"
          >
            {hovered === idx && (
              <span className="absolute inset-0 h-full w-full rounded-md bg-indigo-500" />
            )}
            <span className="relative z-10">{item.name}</span>
          </Link>
        ))}
      </div>
    </nav>
  );
};
export default Navbar;
