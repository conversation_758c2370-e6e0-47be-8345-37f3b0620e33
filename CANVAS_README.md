# 3D Carousel Component

A React/Next.js component that creates a 3D carousel using Three.js. This component was converted from a Vue.js component to work with Next.js and React.

## Features

- **Interactive 3D Carousel**: Displays items in a circular 3D layout
- **Drag to Rotate**: Users can drag to manually rotate the carousel
- **Auto-rotation**: Automatically rotates when not being dragged
- **Responsive**: Adapts to container size changes
- **Touch Support**: Works on mobile devices with touch events
- **Customizable**: Configurable dimensions and styling

## Installation

Make sure you have the required dependencies:

```bash
npm install three @types/three framer-motion clsx
```

## Usage

```tsx
import Canvas from "@/components/canvas";

const MyComponent = () => {
  const images = [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg",
    "https://example.com/image3.jpg",
    // ... more images
  ];

  return (
    <Canvas
      items={images}
      width={400}
      height={500}
      containerClass="bg-white rounded-lg shadow-lg"
      className="cursor-grab"
    />
  );
};
```

## Props

| Prop             | Type       | Default | Description                                        |
| ---------------- | ---------- | ------- | -------------------------------------------------- |
| `items`          | `string[]` | `[]`    | Array of image URLs to display in the carousel     |
| `width`          | `number`   | `450`   | Width of each carousel item in pixels              |
| `height`         | `number`   | `600`   | Height of each carousel item in pixels             |
| `className`      | `string`   | `""`    | Additional CSS classes for the interaction overlay |
| `containerClass` | `string`   | `""`    | Additional CSS classes for the main container      |

## Technical Details

### Dependencies

- **Three.js**: For 3D rendering and scene management
- **CSS3DRenderer**: For rendering HTML/CSS elements in 3D space
- **Framer Motion**: For smooth animations
- **clsx**: For conditional CSS class names

### Key Features

- Uses `CSS3DRenderer` to render HTML elements in 3D space
- Implements drag-to-rotate functionality with mouse and touch events
- Automatic continuous rotation when not being interacted with
- Responsive design that adapts to container size changes
- Proper cleanup of Three.js resources to prevent memory leaks

### Browser Support

- Modern browsers with WebGL support
- Mobile browsers with touch event support

## Customization

You can customize the appearance by:

- Modifying the `containerClass` prop for overall styling
- Adjusting `width` and `height` for item dimensions
- Customizing the CSS classes applied to individual items
- Modifying the rotation speed by changing the animation duration
- Adjusting the drag sensitivity by modifying the `sensitivity` constant

## Performance Considerations

- The component automatically cleans up Three.js resources on unmount
- Animation is paused during drag interactions to improve performance
- Resize events are handled efficiently to maintain responsiveness
