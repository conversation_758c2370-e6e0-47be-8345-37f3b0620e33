{"name": "project-qstar-optimized", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@types/three": "^0.178.0", "clsx": "^2.1.1", "framer-motion": "^12.23.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "three": "^0.178.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "prettier": "^3.6.1", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4", "typescript": "^5"}}