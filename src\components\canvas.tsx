"use client";

import { useRef, useEffect, useState, useCallback } from "react";
import { motion, useMotionValue, useTransform, animate } from "framer-motion";
import Image from "next/image";

interface PortfolioCarouselProps {
  items: string[];
}

const PortfolioCarousel: React.FC<PortfolioCarouselProps> = ({ items }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const dragX = useMotionValue(0);
  const [isDragging, setIsDragging] = useState(false);
  const [autoRotation, setAutoRotation] = useState(0);
  const autoRotationRef = useRef<ReturnType<typeof animate> | null>(null);

  // Create infinite duplicated items for seamless loop
  const infiniteItems = [...items, ...items, ...items];

  const CARD_WIDTH = 220;
  const CARD_HEIGHT = 280;
  const RADIUS = 350;
  const TOTAL_ITEMS = infiniteItems.length;

  // Smooth infinite auto-rotation
  useEffect(() => {
    if (!isDragging) {
      autoRotationRef.current = animate(autoRotation, autoRotation + 360, {
        duration: 25,
        repeat: Infinity,
        ease: "linear",
        onUpdate: (latest) => setAutoRotation(latest),
      });
    } else {
      autoRotationRef.current?.stop();
    }

    return () => autoRotationRef.current?.stop();
  }, [isDragging, autoRotation]);

  const handleDragStart = useCallback(() => {
    setIsDragging(true);
  }, []);

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Create rotation offset for drag interaction
  const rotationOffset = useTransform(dragX, [-300, 300], [90, -90]);

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200">
      {/* Header Section */}
      <div className="relative z-10 px-6 pt-16 text-center">
        <h1 className="mb-4 text-5xl font-bold text-gray-900">
          Propel Your Business Forward
        </h1>
        <p className="mb-2 text-xl text-gray-600">
          Our expert creators, ready to elevate your brand
        </p>
        <p className="mb-8 text-sm text-gray-500">0:08 / 0:10</p>
        <button className="rounded-full bg-red-500 px-8 py-3 font-semibold text-white shadow-lg transition-all duration-300 hover:scale-105 hover:bg-red-600 hover:shadow-xl">
          Get Started
        </button>
      </div>

      {/* Infinite 3D Carousel */}
      <div className="relative mt-20 flex h-[500px] items-center justify-center overflow-hidden">
        <motion.div
          ref={containerRef}
          className="relative h-full w-full"
          style={{
            perspective: 1200,
            perspectiveOrigin: "center center",
          }}
        >
          <motion.div
            className="absolute top-1/2 left-1/2 cursor-grab active:cursor-grabbing"
            style={{
              transformStyle: "preserve-3d",
              x: "-50%",
              y: "-50%",
            }}
            drag="x"
            dragConstraints={{ left: -400, right: 400 }}
            dragElastic={0.1}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            whileDrag={{ scale: 1.02 }}
          >
            {infiniteItems.map((src, i) => {
              const angle = (360 / TOTAL_ITEMS) * i;
              const currentRotation = isDragging
                ? rotationOffset
                : autoRotation;

              return (
                <motion.div
                  key={`${src}-${i}`}
                  className="absolute"
                  style={{
                    transformStyle: "preserve-3d",
                    rotateY: currentRotation.get
                      ? currentRotation.get() + angle
                      : currentRotation + angle,
                    translateZ: RADIUS,
                    x: -CARD_WIDTH / 2,
                    y: -CARD_HEIGHT / 2,
                  }}
                  whileHover={{
                    scale: 1.08,
                    rotateX: -8,
                    transition: { duration: 0.3 },
                  }}
                >
                  <div className="group hover:shadow-3xl relative overflow-hidden rounded-2xl bg-white/95 shadow-2xl backdrop-blur-sm transition-all duration-500 hover:bg-white">
                    {/* Curved inner container */}
                    <div className="relative h-full w-full overflow-hidden rounded-2xl">
                      <Image
                        src={src}
                        alt={`Portfolio item ${(i % items.length) + 1}`}
                        width={CARD_WIDTH}
                        height={CARD_HEIGHT}
                        className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-110"
                        style={{
                          width: CARD_WIDTH,
                          height: CARD_HEIGHT,
                        }}
                        priority={i < 6}
                        loading={i < 6 ? "eager" : "lazy"}
                      />

                      {/* Curved gradient overlay */}
                      <div className="absolute inset-0 rounded-2xl bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

                      {/* Content overlay with curved edges */}
                      <div className="absolute right-0 bottom-0 left-0 translate-y-full transform rounded-b-2xl bg-gradient-to-t from-black/80 to-transparent p-4 text-white transition-transform duration-300 group-hover:translate-y-0">
                        <h3 className="mb-1 text-sm font-semibold">
                          Creative Work
                        </h3>
                        <p className="text-xs opacity-90">
                          Professional Design
                        </p>
                      </div>
                    </div>

                    {/* Enhanced shine effect */}
                    <div className="absolute inset-0 -translate-x-full rounded-2xl bg-gradient-to-r from-transparent via-white/30 to-transparent transition-transform duration-1000 group-hover:translate-x-full" />
                  </div>
                </motion.div>
              );
            })}
          </motion.div>
        </motion.div>

        {/* Subtle navigation hint */}
        <div className="absolute bottom-8 left-1/2 -translate-x-1/2 text-center">
          <p className="text-sm text-gray-500">
            Drag to explore • Infinite 3D rotation
          </p>
        </div>
      </div>
    </div>
  );
};

export default PortfolioCarousel;
