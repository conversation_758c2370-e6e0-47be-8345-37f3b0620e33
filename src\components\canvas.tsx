"use client";

import { useRef, useEffect, useState, useCallback } from "react";
import { motion, useMotionValue } from "framer-motion";
import Image from "next/image";

interface PortfolioCarouselProps {
  items: string[];
}

const PortfolioCarousel: React.FC<PortfolioCarouselProps> = ({ items }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const dragX = useMotionValue(0);
  const [isDragging, setIsDragging] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);

  // Auto-scroll effect
  useEffect(() => {
    if (!isDragging) {
      const interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % items.length);
      }, 3000);
      return () => clearInterval(interval);
    }
  }, [isDragging, items.length]);

  useEffect(() => {
    const updateContainerWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    };

    updateContainerWidth();
    window.addEventListener("resize", updateContainerWidth);
    return () => window.removeEventListener("resize", updateContainerWidth);
  }, []);

  const handleDragStart = useCallback(() => {
    setIsDragging(true);
  }, []);

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  const CARD_WIDTH = 280;
  const CARD_HEIGHT = 360;
  const CARD_GAP = 24;

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header Section */}
      <div className="relative z-10 px-6 pt-16 text-center">
        <h1 className="mb-4 text-5xl font-bold text-gray-900">
          Propel Your Business Forward
        </h1>
        <p className="mb-2 text-xl text-gray-600">
          Our expert creators, ready to elevate your brand
        </p>
        <p className="mb-8 text-sm text-gray-500">0:08 / 0:10</p>
        <button className="rounded-full bg-red-500 px-8 py-3 font-semibold text-white shadow-lg transition-all duration-300 hover:scale-105 hover:bg-red-600 hover:shadow-xl">
          Get Started
        </button>
      </div>

      {/* Modern 3D Carousel */}
      <div className="relative mt-16 overflow-hidden">
        <motion.div
          ref={containerRef}
          className="flex items-center justify-center"
          style={{ perspective: 1000 }}
        >
          <motion.div
            className="flex cursor-grab gap-6 active:cursor-grabbing"
            drag="x"
            dragConstraints={{
              left: -(items.length * (CARD_WIDTH + CARD_GAP) - containerWidth),
              right: 0,
            }}
            dragElastic={0.2}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            style={{ x: dragX }}
            animate={{
              x: isDragging
                ? undefined
                : -currentIndex * (CARD_WIDTH + CARD_GAP),
            }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 30,
            }}
          >
            {items.map((src, i) => (
              <motion.div
                key={i}
                className="relative flex-shrink-0"
                style={{
                  width: CARD_WIDTH,
                  height: CARD_HEIGHT,
                }}
                whileHover={{
                  scale: 1.05,
                  rotateY: -5,
                  transition: { duration: 0.3 },
                }}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: i * 0.1 }}
              >
                <div className="group hover:shadow-3xl relative h-full w-full overflow-hidden rounded-3xl bg-white shadow-2xl transition-all duration-500">
                  {/* Image */}
                  <div className="relative h-full w-full overflow-hidden">
                    <Image
                      src={src}
                      alt={`Portfolio item ${i + 1}`}
                      fill
                      className="object-cover transition-transform duration-700 group-hover:scale-110"
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    />

                    {/* Gradient Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

                    {/* Content Overlay */}
                    <div className="absolute right-0 bottom-0 left-0 translate-y-full transform p-6 text-white transition-transform duration-300 group-hover:translate-y-0">
                      <h3 className="mb-1 text-lg font-semibold">
                        Creative Project
                      </h3>
                      <p className="text-sm opacity-90">Professional Design</p>
                    </div>
                  </div>

                  {/* Shine Effect */}
                  <div className="absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent transition-transform duration-1000 group-hover:translate-x-full" />
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Navigation Dots */}
        <div className="mt-12 flex justify-center space-x-3">
          {items.slice(0, 6).map((_, i) => (
            <button
              key={i}
              onClick={() => setCurrentIndex(i)}
              className={`h-3 w-3 rounded-full transition-all duration-300 ${
                i === currentIndex % 6
                  ? "scale-125 bg-red-500"
                  : "bg-gray-300 hover:bg-gray-400"
              }`}
            />
          ))}
        </div>

        {/* Navigation Instructions */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            Drag to explore • Auto-advancing • Click dots to navigate
          </p>
        </div>
      </div>
    </div>
  );
};

export default PortfolioCarousel;
