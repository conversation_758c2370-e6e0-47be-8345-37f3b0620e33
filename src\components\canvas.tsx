"use client";

import { useRef, useEffect, useState } from "react";
import { motion, useMotionValue, useTransform } from "framer-motion";
import Image from "next/image";

interface PortfolioCarouselProps {
  items: string[];
}

const CARD_WIDTH = 200;
const CARD_GAP = 20;

const PortfolioCarousel: React.FC<PortfolioCarouselProps> = ({ items }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const dragX = useMotionValue(0);
  const [containerWidth, setContainerWidth] = useState(0);

  useEffect(() => {
    if (containerRef.current) {
      setContainerWidth(containerRef.current.offsetWidth);
    }
  }, []);

  return (
    <div className="overflow-hidden py-10">
      {/* Text Overlay and Button */}
      <div className="mb-6 text-center">
        <p className="text-lg font-medium">
          our expert creators, learn ready to propel your business forward
        </p>
        <p className="text-sm text-gray-500">0:08 / 0:10</p>
        <button className="mt-2 rounded-lg bg-red-500 px-6 py-2 text-white transition-colors hover:bg-red-600">
          Get Started
        </button>
      </div>

      {/* Carousel Container */}
      <motion.div
        ref={containerRef}
        className="flex items-center justify-center overflow-hidden"
        style={{ perspective: 1200 }}
      >
        <motion.div
          className="flex cursor-grab gap-[20px] active:cursor-grabbing"
          drag="x"
          dragConstraints={{
            left: -(items.length * (CARD_WIDTH + CARD_GAP) - containerWidth),
            right: 0,
          }}
          style={{ x: dragX }}
        >
          {items.map((src, i) => {
            const baseX = i * (CARD_WIDTH + CARD_GAP);
            const offset = useTransform(dragX, (latestX) => {
              const center = containerWidth / 2 - CARD_WIDTH / 2;
              const distanceFromCenter = baseX + latestX - center;
              const curveFactor = Math.sin(
                (distanceFromCenter / containerWidth) * Math.PI
              ); // Creates a slight curve

              const rotateY = (-distanceFromCenter / 10).toFixed(2);
              const scale = Math.max(
                0.7,
                1 - Math.abs(distanceFromCenter) / 800
              );
              const zIndex = 1000 - Math.abs(distanceFromCenter);
              const translateY = curveFactor * -20; // Vertical curve effect

              return {
                rotateY,
                scale,
                zIndex,
                translateY,
              };
            });

            return (
              <motion.div
                key={i}
                className="relative h-[300px] w-[200px] overflow-hidden rounded-[20px] bg-white shadow-xl"
                style={{
                  rotateY: offset.rotateY,
                  scale: offset.scale,
                  zIndex: offset.zIndex,
                  y: offset.translateY,
                }}
              >
                <Image
                  src={src}
                  alt={`img-${i}`}
                  width={200}
                  height={300}
                  className="h-full w-full object-cover"
                />
              </motion.div>
            );
          })}
        </motion.div>
      </motion.div>
    </div>
  );
};

export default PortfolioCarousel;
