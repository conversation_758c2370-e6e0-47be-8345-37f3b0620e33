"use client";

import { useRef, useEffect, useState, useCallback } from "react";
import { motion, useMotionValue, useTransform, animate } from "framer-motion";
import Image from "next/image";

interface PortfolioCarouselProps {
  items: string[];
}

const CARD_WIDTH = 180;
const CARD_HEIGHT = 240;
const RADIUS = 400;

const PortfolioCarousel: React.FC<PortfolioCarouselProps> = ({ items }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const dragX = useMotionValue(0);
  const [isDragging, setIsDragging] = useState(false);
  const [autoRotation, setAutoRotation] = useState(0);
  const autoRotationRef = useRef<ReturnType<typeof animate> | null>(null);

  // Create rotation offset transform outside of render loop
  const rotationOffset = useTransform(dragX, [-200, 200], [60, -60]);

  // Auto-rotation effect
  useEffect(() => {
    if (!isDragging) {
      autoRotationRef.current = animate(autoRotation, autoRotation + 360, {
        duration: 20,
        repeat: Infinity,
        ease: "linear",
        onUpdate: (latest) => setAutoRotation(latest),
      });
    } else {
      autoRotationRef.current?.stop();
    }

    return () => autoRotationRef.current?.stop();
  }, [isDragging, autoRotation]);

  const handleDragStart = useCallback(() => {
    setIsDragging(true);
  }, []);

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-purple-900/20 via-slate-900/40 to-black"></div>
      <div className="absolute inset-0 opacity-30">
        <div className="h-full w-full animate-pulse bg-gradient-to-r from-transparent via-purple-500/10 to-transparent"></div>
      </div>

      {/* Text Overlay and Button */}
      <div className="relative z-10 pt-20 text-center">
        <h1 className="mb-4 bg-gradient-to-r from-white via-purple-200 to-white bg-clip-text text-4xl font-bold text-transparent">
          Propel Your Business Forward
        </h1>
        <p className="mb-2 text-lg font-medium text-gray-300">
          Our expert creators, ready to elevate your brand
        </p>
        <p className="mb-6 text-sm text-gray-400">Interactive 3D Portfolio</p>
        <button className="group relative overflow-hidden rounded-full bg-gradient-to-r from-red-500 to-pink-600 px-8 py-3 text-white shadow-2xl transition-all duration-300 hover:from-red-600 hover:to-pink-700 hover:shadow-red-500/25">
          <span className="relative z-10">Get Started</span>
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
        </button>
      </div>

      {/* 3D Carousel Container */}
      <div className="relative flex h-[600px] items-center justify-center overflow-hidden">
        <motion.div
          ref={containerRef}
          className="relative h-full w-full"
          style={{
            perspective: 1200,
            perspectiveOrigin: "center center",
          }}
        >
          <motion.div
            className="absolute top-1/2 left-1/2 cursor-grab active:cursor-grabbing"
            style={{
              transformStyle: "preserve-3d",
              x: "-50%",
              y: "-50%",
            }}
            drag="x"
            dragConstraints={{ left: -200, right: 200 }}
            dragElastic={0.1}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            whileDrag={{ scale: 1.05 }}
          >
            {items.map((src, i) => {
              const angle = (360 / items.length) * i;

              return (
                <motion.div
                  key={i}
                  className="absolute"
                  style={{
                    transformStyle: "preserve-3d",
                    rotateY: isDragging ? rotationOffset : autoRotation + angle,
                    translateZ: RADIUS,
                    x: -CARD_WIDTH / 2,
                    y: -CARD_HEIGHT / 2,
                  }}
                  whileHover={{
                    scale: 1.1,
                    rotateX: -5,
                    transition: { duration: 0.3 },
                  }}
                >
                  <div className="group relative overflow-hidden rounded-2xl border border-white/20 bg-white/10 shadow-2xl backdrop-blur-sm transition-all duration-500 hover:bg-white/20 hover:shadow-purple-500/20">
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                    <Image
                      src={src}
                      alt={`Portfolio item ${i + 1}`}
                      width={CARD_WIDTH}
                      height={CARD_HEIGHT}
                      className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-110"
                      style={{
                        width: CARD_WIDTH,
                        height: CARD_HEIGHT,
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100"></div>
                    <div className="absolute right-4 bottom-4 left-4 translate-y-4 opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100">
                      <p className="text-sm font-medium text-white">
                        Portfolio Item {i + 1}
                      </p>
                      <p className="text-xs text-gray-300">Creative Design</p>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </motion.div>
        </motion.div>

        {/* Navigation Hints */}
        <div className="absolute bottom-8 left-1/2 -translate-x-1/2 text-center">
          <p className="mb-2 text-sm text-gray-400">
            Drag to rotate • Auto-rotating
          </p>
          <div className="flex justify-center space-x-2">
            <div className="h-2 w-2 animate-pulse rounded-full bg-white/30"></div>
            <div
              className="h-2 w-2 animate-pulse rounded-full bg-white/50"
              style={{ animationDelay: "0.2s" }}
            ></div>
            <div
              className="h-2 w-2 animate-pulse rounded-full bg-white/30"
              style={{ animationDelay: "0.4s" }}
            ></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PortfolioCarousel;
