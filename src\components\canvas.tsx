"use client";

import { useRef, useEffect, useState, useCallback } from "react";
import { motion, animate } from "framer-motion";
import Image from "next/image";

interface PortfolioCarouselProps {
  items: string[];
}

const PortfolioCarousel: React.FC<PortfolioCarouselProps> = ({ items }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [rotation, setRotation] = useState(0);
  const animationRef = useRef<ReturnType<typeof animate> | null>(null);

  // Constants for the 3D carousel
  const CARD_WIDTH = 200;
  const CARD_HEIGHT = 260;
  const RADIUS = 400;

  // Auto-rotation effect
  useEffect(() => {
    if (!isDragging) {
      animationRef.current = animate(rotation, rotation + 360, {
        duration: 20,
        repeat: Infinity,
        ease: "linear",
        onUpdate: (latest) => setRotation(latest),
      });
    } else {
      animationRef.current?.stop();
    }

    return () => {
      if (animationRef.current) {
        animationRef.current.stop();
      }
    };
  }, [isDragging, rotation]);

  const handleDragStart = useCallback(() => {
    setIsDragging(true);
  }, []);

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 animate-pulse bg-gradient-to-r from-blue-400/10 via-purple-400/10 to-pink-400/10"></div>
        <div className="absolute top-1/4 left-1/4 h-96 w-96 animate-bounce rounded-full bg-blue-400/20 blur-3xl"></div>
        <div
          className="absolute right-1/4 bottom-1/4 h-96 w-96 animate-bounce rounded-full bg-purple-400/20 blur-3xl"
          style={{ animationDelay: "1s" }}
        ></div>
      </div>

      {/* Header Section */}
      <div className="relative z-10 px-6 pt-16 text-center">
        <motion.h1
          className="mb-4 bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-6xl font-bold text-transparent"
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          Propel Your Business Forward
        </motion.h1>
        <motion.p
          className="mb-2 text-xl text-gray-700"
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          Our expert creators, ready to elevate your brand
        </motion.p>
        <motion.p
          className="mb-8 text-sm text-gray-500"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          0:08 / 0:10
        </motion.p>
        <motion.button
          className="rounded-full bg-gradient-to-r from-red-500 to-pink-600 px-8 py-3 font-semibold text-white shadow-xl transition-all duration-300 hover:scale-110 hover:from-red-600 hover:to-pink-700 hover:shadow-2xl"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          Get Started
        </motion.button>
      </div>

      {/* Perfect 3D Carousel */}
      <div className="relative mt-20 flex h-[600px] items-center justify-center overflow-hidden">
        <motion.div
          ref={containerRef}
          className="relative h-full w-full"
          style={{
            perspective: 1500,
            perspectiveOrigin: "center center",
          }}
        >
          <motion.div
            className="absolute top-1/2 left-1/2 cursor-grab active:cursor-grabbing"
            style={{
              transformStyle: "preserve-3d",
              x: "-50%",
              y: "-50%",
            }}
            drag="x"
            dragConstraints={{ left: -300, right: 300 }}
            dragElastic={0.2}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            whileDrag={{ scale: 1.05 }}
          >
            {items.map((src, i) => {
              const angle = (360 / items.length) * i;
              const rotateY = rotation + angle;

              return (
                <motion.div
                  key={i}
                  className="absolute"
                  style={{
                    transformStyle: "preserve-3d",
                    rotateY: rotateY,
                    translateZ: RADIUS,
                    x: -CARD_WIDTH / 2,
                    y: -CARD_HEIGHT / 2,
                  }}
                  initial={{ opacity: 0, scale: 0.5 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{
                    duration: 0.8,
                    delay: i * 0.1,
                    type: "spring",
                    stiffness: 100,
                  }}
                  whileHover={{
                    scale: 1.15,
                    rotateX: -10,
                    z: 100,
                    transition: { duration: 0.4 },
                  }}
                >
                  <div className="group hover:shadow-3xl relative overflow-hidden rounded-3xl border border-white/20 bg-white/90 shadow-2xl backdrop-blur-lg transition-all duration-500 hover:bg-white">
                    {/* Image Container */}
                    <div className="relative h-full w-full overflow-hidden rounded-3xl">
                      <Image
                        src={src}
                        alt={`Creative work ${i + 1}`}
                        width={CARD_WIDTH}
                        height={CARD_HEIGHT}
                        className="h-full w-full object-cover transition-all duration-700 group-hover:scale-125 group-hover:rotate-2"
                        style={{
                          width: CARD_WIDTH,
                          height: CARD_HEIGHT,
                        }}
                        priority={i < 4}
                        loading={i < 4 ? "eager" : "lazy"}
                      />

                      {/* Gradient Overlay */}
                      <div className="absolute inset-0 rounded-3xl bg-gradient-to-t from-black/70 via-black/20 to-transparent opacity-0 transition-all duration-500 group-hover:opacity-100" />

                      {/* Content Overlay */}
                      <div className="absolute right-0 bottom-0 left-0 translate-y-full transform p-6 text-white transition-all duration-500 group-hover:translate-y-0">
                        <h3 className="mb-2 text-lg font-bold drop-shadow-lg">
                          Creative Portfolio
                        </h3>
                        <p className="text-sm opacity-90 drop-shadow">
                          Professional Design Work
                        </p>
                        <div className="mt-3 flex space-x-2">
                          <div className="h-2 w-2 rounded-full bg-white/60"></div>
                          <div className="h-2 w-2 rounded-full bg-white/40"></div>
                          <div className="h-2 w-2 rounded-full bg-white/20"></div>
                        </div>
                      </div>
                    </div>

                    {/* Premium Shine Effect */}
                    <div className="absolute inset-0 -translate-x-full rounded-3xl bg-gradient-to-r from-transparent via-white/40 to-transparent transition-transform duration-1000 group-hover:translate-x-full" />

                    {/* Border Glow */}
                    <div className="absolute inset-0 -z-10 rounded-3xl border-2 border-transparent bg-gradient-to-r from-blue-400/50 via-purple-400/50 to-pink-400/50 opacity-0 blur-sm transition-opacity duration-500 group-hover:opacity-100" />
                  </div>
                </motion.div>
              );
            })}
          </motion.div>
        </motion.div>

        {/* Enhanced Navigation Hint */}
        <motion.div
          className="absolute bottom-8 left-1/2 -translate-x-1/2 text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
        >
          <p className="mb-3 text-sm font-medium text-gray-600">
            Drag to explore • Infinite 3D rotation
          </p>
          <div className="flex justify-center space-x-2">
            <div className="h-2 w-2 animate-pulse rounded-full bg-blue-400"></div>
            <div
              className="h-2 w-2 animate-pulse rounded-full bg-purple-400"
              style={{ animationDelay: "0.2s" }}
            ></div>
            <div
              className="h-2 w-2 animate-pulse rounded-full bg-pink-400"
              style={{ animationDelay: "0.4s" }}
            ></div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default PortfolioCarousel;
